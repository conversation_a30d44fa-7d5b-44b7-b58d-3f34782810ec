# Nested MCP Server Architecture

This directory contains an implementation of a "nested" or "meta" MCP server approach, where a single MCP server acts as a proxy/aggregator for multiple backend MCP servers.

## Architecture Overview

```
Client Applications
        ↓
   Meta-MCP Server (Port 8080)
        ↓
┌─────────────────────────────────┐
│  Backend MCP Servers:           │
│  • Gaia HTTP (Port 9000)       │
│  • Gaia SSE (Port 9000)        │
│  • Firecrawl (Hosted)          │
│  • Custom Servers...           │
└─────────────────────────────────┘
```

## Benefits of Nested Approach

### For Clients
- **Single Connection Point**: Connect to one server instead of managing multiple connections
- **Transparent Access**: All backend tools appear as if they're from one server
- **Simplified Configuration**: No need to know about backend server details
- **Automatic Load Balancing**: Meta-server can distribute load across backends

### For Server Management
- **Centralized Control**: Manage all backend servers from one place
- **Tool Namespacing**: Resolve tool name conflicts with prefixes
- **Health Monitoring**: Centralized health checking of all backends
- **Failover Support**: Can implement automatic failover between backends

### For Development
- **Easy Testing**: Test against one endpoint instead of multiple
- **Gradual Migration**: Add/remove backend servers without client changes
- **Tool Discovery**: Centralized tool registry and documentation

## Files

1. **`nested_mcp_server.py`** - Meta-MCP server implementation
2. **`nested_mcp_client_example.py`** - Client demo for the nested server
3. **`NESTED_MCP_README.md`** - This documentation

## Quick Start

### 1. Create Configuration

```bash
# Create example configuration
python nested_mcp_server.py --create-config
```

This creates `nested_servers.json`:

```json
{
  "description": "Nested MCP Server Configuration",
  "meta_server": {
    "name": "Unified MCP Gateway",
    "port": 8080,
    "host": "0.0.0.0"
  },
  "backend_servers": {
    "gaia_http": {
      "enabled": true,
      "url": "http://0.0.0.0:9000/mcp",
      "protocol": "http",
      "use_prefix": false
    },
    "gaia_sse": {
      "enabled": true,
      "url": "http://0.0.0.0:9000/sse",
      "protocol": "sse",
      "use_prefix": true,
      "prefix": "sse"
    }
  }
}
```

### 2. Start Backend Servers

```bash
# Terminal 1: Start Gaia HTTP server
cd gaia/gaia_ceto/proto_mcp_http
python mcp_http_server.py --port 9000

# Terminal 2: Start Gaia SSE server  
cd gaia/gaia_ceto/proto_mcp
python mcp_sse_server.py --port 9000
```

### 3. Start Meta-Server

```bash
# Terminal 3: Start the nested MCP server
python nested_mcp_server.py --port 8080 --config nested_servers.json
```

### 4. Connect Client

```bash
# Terminal 4: Run client demo
python nested_mcp_client_example.py --server http://localhost:8080/mcp
```

## Configuration Options

### Backend Server Configuration

```json
{
  "server_id": {
    "enabled": true,              // Enable/disable this backend
    "url": "http://host:port/mcp", // Backend server URL
    "protocol": "http",           // "http" or "sse"
    "description": "Server desc", // Human-readable description
    "use_prefix": false,          // Whether to prefix tool names
    "prefix": "custom",           // Custom prefix (default: server_id)
    "environment_required": ["API_KEY"] // Required env vars
  }
}
```

### Tool Naming Strategies

1. **No Prefix** (`use_prefix: false`):
   - Tools keep original names: `echostring`, `get_data`
   - Risk of name conflicts between backends

2. **Automatic Prefix** (`use_prefix: true`):
   - Tools get prefixed: `gaia_echostring`, `api_get_data`
   - Prevents conflicts but longer names

3. **Conflict Resolution**:
   - If conflicts detected, automatic prefixing is applied
   - Original tool names preserved in mapping

## Meta-Server Tools

The nested server automatically provides these management tools:

### `list_backend_servers`
Lists all connected backend servers and their status.

```python
result = await client.call_tool("list_backend_servers", {})
```

### `get_tool_mapping`
Shows mapping between meta-tools and backend servers.

```python
result = await client.call_tool("get_tool_mapping", {})
```

### `backend_server_health`
Checks health status of all backend servers.

```python
result = await client.call_tool("backend_server_health", {})
```

## Usage Examples

### Basic Tool Call

```python
from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib

# Connect to meta-server (not individual backends)
client = MCPClientLib()
await client.connect_to_server("http://localhost:8080/mcp")

# Call any tool from any backend server
result = await client.call_tool("echostring", "Hello World!")
result = await client.call_tool("sse_echostring", "Hello from SSE!")
```

### LLM Query with Multiple Backend Tools

```python
# The LLM can use tools from any backend server transparently
result = await client.process_query(
    "Check the health of backend servers and echo a status message"
)
```

### Tool Discovery

```python
# Get all available tools (from all backends)
tools = client.available_tools
for tool in tools:
    print(f"{tool['name']}: {tool['description']}")
```

## Advanced Features

### Environment Variable Substitution

Backend URLs can use environment variables:

```json
{
  "firecrawl": {
    "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
    "environment_required": ["FIRECRAWL_API_KEY"]
  }
}
```

### Progress Reporting

Meta-tools automatically report progress:

```
[INFO] Calling echostring on gaia_http
[INFO] Processing result  
[INFO] Complete
```

### Error Handling

- Backend connection failures don't crash the meta-server
- Tool call errors are properly propagated to clients
- Health checking identifies problematic backends

### Load Balancing (Future Enhancement)

The architecture supports adding load balancing:

```python
# Potential future feature
async def call_tool_with_balancing(self, tool_name, tool_input):
    # Find all backends that have this tool
    candidates = self.find_tool_servers(tool_name)
    
    # Select based on load, health, etc.
    selected_server = self.select_best_server(candidates)
    
    return await self.call_backend_tool(selected_server, tool_name, tool_input)
```

## Comparison: Direct vs Nested

### Direct Multi-Client Approach
```python
# Client manages multiple connections
client1 = MCPClientLib()
client2 = MCPClientLib()
await client1.connect_to_server("http://server1:9000/mcp")
await client2.connect_to_server("http://server2:9001/mcp")

# Client must know which server has which tool
result = await client1.call_tool("echostring", "hello")
```

### Nested Server Approach
```python
# Client connects to one meta-server
client = MCPClientLib()
await client.connect_to_server("http://meta-server:8080/mcp")

# All tools available through single interface
result = await client.call_tool("echostring", "hello")  # Auto-routed
```

## Deployment Scenarios

### Development Environment
- All servers on localhost with different ports
- Easy testing and debugging
- Quick reconfiguration

### Production Environment
- Meta-server as API gateway
- Backend servers on different hosts/containers
- Load balancing and health monitoring
- Centralized logging and metrics

### Hybrid Environment
- Mix of local and remote backend servers
- Some backends behind firewalls
- Meta-server as secure proxy

## Performance Considerations

### Latency
- Additional hop adds ~1-5ms latency
- Negligible for most use cases
- Can be optimized with connection pooling

### Throughput
- Meta-server can handle concurrent requests
- Backend connections are reused
- Bottleneck typically at backend servers

### Memory Usage
- Meta-server maintains connections to all backends
- Tool schema caching reduces memory usage
- Scales linearly with number of backends

## Monitoring and Debugging

### Logging
```python
# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

# Meta-server logs all backend interactions
logger.info(f"Calling {tool_name} on {server_id}")
logger.debug(f"Backend response: {result}")
```

### Health Checks
```python
# Automated health monitoring
result = await client.call_tool("backend_server_health", {})
```

### Metrics (Future Enhancement)
- Tool call counts per backend
- Response times and error rates
- Backend availability statistics

This nested approach provides a clean, scalable way to aggregate multiple MCP servers while maintaining the simplicity of a single connection point for clients.
