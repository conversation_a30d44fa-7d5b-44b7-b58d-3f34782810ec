#!/usr/bin/env python3
"""
Multi-MCP Client Example

This script demonstrates how to connect to multiple MCP servers simultaneously
and use tools from different servers in a single Python client.

Features:
    - Connect to multiple MCP servers (HTTP and SSE protocols)
    - Auto-route tool calls to the appropriate server
    - Aggregate tools from all connected servers
    - Process queries using any connected server
    - Clean connection management

Usage:
    python multi_mcp_client.py

Example server configurations:
    # Local servers
    gaia_http: http://0.0.0.0:9000/mcp (HTTP protocol)
    gaia_sse: http://0.0.0.0:9000/sse (SSE protocol)

    # Third-party servers (with API keys)
    firecrawl: https://mcp.firecrawl.dev/{API_KEY}/sse (SSE protocol)

Requirements:
    - MCP client libraries (mcp_http_clientlib, mcp_sse_clientlib)
    - Anthropic API key in environment (ANTHROPIC_API_KEY)
    - Running MCP servers
"""

import asyncio
import logging
import os
import sys
from typing import Dict, List, Optional, Any
from contextlib import AsyncExitStack
from datetime import datetime

# Import MCP client libraries - handle both relative and absolute imports
try:
    # Try relative imports first (when used as module)
    from .mcp_http_clientlib import MC<PERSON>lient<PERSON>ib as MCPHTTPClientLib
    from ..proto_mcp.mcp_sse_clientlib import MCPClientLib as MCPSSEClientLib
except ImportError:
    try:
        # Fall back to absolute imports (when run directly)
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
        from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib as MCPHTTPClientLib
        from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib as MCPSSEClientLib
    except ImportError as e:
        print(f"Error importing MCP client libraries: {e}")
        print("Make sure you're running this from the correct directory")
        sys.exit(1)

# Import MCP stdio client for process spawning
try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    STDIO_AVAILABLE = True
except ImportError:
    print("Warning: MCP stdio client not available. Process spawning will be disabled.")
    STDIO_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ToolCallResult:
    """Result of a tool call, mimicking the interface from HTTP/SSE clients."""
    def __init__(self, success: bool, content: Any = None, error: str = None, execution_time: float = 0.0):
        self.success = success
        self.content = content
        self.error = error
        self.execution_time = execution_time


class StdioMCPWrapper:
    """Wrapper around MCP stdio session to mimic HTTP/SSE client interface."""

    def __init__(self, session: 'ClientSession', available_tools: List[Dict[str, Any]]):
        self.session = session
        self.available_tools = available_tools

    async def call_tool(self, tool_name: str, tool_input: Any, tool_call_id: str = "call", timeout: float = 30.0) -> ToolCallResult:
        """Call a tool via stdio session."""
        import time
        start_time = time.time()

        try:
            # Convert tool_input to proper format if it's a string
            if isinstance(tool_input, str):
                # For simple string inputs, wrap in a generic parameter
                arguments = {"input": tool_input}
            elif isinstance(tool_input, dict):
                arguments = tool_input
            else:
                # Try to convert to string
                arguments = {"input": str(tool_input)}

            logger.debug(f"Calling tool {tool_name} with arguments: {arguments}")

            # Call the tool
            result = await self.session.call_tool(tool_name, arguments)

            execution_time = time.time() - start_time

            logger.debug(f"Raw tool result: {result}")
            logger.debug(f"Result type: {type(result)}")
            logger.debug(f"Result attributes: {dir(result)}")

            # Check if the result indicates an error
            if hasattr(result, 'isError') and result.isError:
                error_msg = "Tool execution failed"
                if hasattr(result, 'content') and result.content:
                    error_msg = str(result.content)
                return ToolCallResult(
                    success=False,
                    error=error_msg,
                    execution_time=execution_time
                )

            # Extract content from result
            content = None
            if hasattr(result, 'content') and result.content:
                # Handle different content types
                content_items = result.content
                if isinstance(content_items, list) and len(content_items) > 0:
                    # Get the first content item
                    first_item = content_items[0]
                    if hasattr(first_item, 'text'):
                        content = first_item.text
                    elif hasattr(first_item, 'data'):
                        content = first_item.data
                    else:
                        content = str(first_item)
                else:
                    content = str(content_items)

            # If no content was extracted, check for other result formats
            if content is None:
                if hasattr(result, 'text'):
                    content = result.text
                elif hasattr(result, 'data'):
                    content = result.data
                else:
                    content = str(result)

            # If content is still None or empty, that might indicate an error
            if not content:
                logger.warning(f"Tool {tool_name} returned empty content. Result: {result}")
                return ToolCallResult(
                    success=False,
                    error="Tool returned empty result",
                    execution_time=execution_time
                )

            return ToolCallResult(
                success=True,
                content=content,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Exception in tool call: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return ToolCallResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )


class MultiMCPClient:
    """A client that can connect to multiple MCP servers simultaneously."""
    
    def __init__(self, anthropic_api_key: Optional[str] = None):
        """Initialize the multi-MCP client.
        
        Args:
            anthropic_api_key: Optional Anthropic API key. If not provided,
                             will try to get from environment.
        """
        self.anthropic_api_key = anthropic_api_key or os.getenv('ANTHROPIC_API_KEY')
        self.connections: Dict[str, Dict[str, Any]] = {}
        self.exit_stack = AsyncExitStack()
        
    def debug_callback(self, level: str, message: str, data: Any = None):
        """Debug callback for MCP clients."""
        logger.log(getattr(logging, level.upper(), logging.INFO), f"MCP: {message}")
        if data and level == "debug":
            logger.debug(f"  Data: {data}")
        
    async def add_server(self, 
                        server_id: str,
                        server_url: str, 
                        protocol: str = "http",
                        description: str = "") -> bool:
        """Add and connect to an MCP server.
        
        Args:
            server_id: Unique identifier for this server
            server_url: URL of the MCP server
            protocol: Protocol to use ("http" or "sse")
            description: Optional description of the server
            
        Returns:
            True if connection was successful
        """
        if server_id in self.connections:
            logger.warning(f"Server {server_id} already exists")
            return False
            
        logger.info(f"Connecting to {server_id} ({protocol}) at {server_url}")
        
        try:
            # Create the appropriate client based on protocol
            if protocol.lower() == "http":
                client = MCPHTTPClientLib(
                    anthropic_api_key=self.anthropic_api_key,
                    debug_callback=self.debug_callback
                )
            elif protocol.lower() == "sse":
                client = MCPSSEClientLib(
                    anthropic_api_key=self.anthropic_api_key,
                    debug_callback=self.debug_callback
                )
            else:
                logger.error(f"Unsupported protocol: {protocol}")
                return False
                
            # Connect to the server
            success = await client.connect_to_server(server_url)
            
            if success:
                # Store connection info
                self.connections[server_id] = {
                    'client': client,
                    'url': server_url,
                    'protocol': protocol,
                    'description': description,
                    'connected_at': datetime.now(),
                    'tools': client.available_tools
                }
                
                tool_names = [tool['name'] for tool in client.available_tools]
                logger.info(f"Successfully connected to {server_id} with tools: {', '.join(tool_names)}")
                return True
            else:
                logger.error(f"Failed to connect to {server_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error connecting to {server_id}: {e}")
            return False

    async def spawn_server(self,
                          server_id: str,
                          command: str,
                          args: List[str],
                          env: Dict[str, str] = None,
                          description: str = "") -> bool:
        """Spawn an MCP server process and connect to it via stdio.

        Args:
            server_id: Unique identifier for this server
            command: Command to run (e.g., "npx", "python")
            args: Arguments for the command (e.g., ["-y", "exa-mcp"])
            env: Environment variables to set
            description: Optional description of the server

        Returns:
            True if spawning and connection was successful
        """
        if not STDIO_AVAILABLE:
            logger.error("MCP stdio client not available. Cannot spawn processes.")
            return False

        if server_id in self.connections:
            logger.warning(f"Server {server_id} already exists")
            return False

        logger.info(f"Spawning MCP server {server_id}: {command} {' '.join(args)}")

        try:
            # Prepare environment variables
            process_env = os.environ.copy()
            if env:
                process_env.update(env)

            # Create stdio server parameters
            server_params = StdioServerParameters(
                command=command,
                args=args,
                env=process_env
            )

            # Create stdio client and session
            stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
            read_stream, write_stream = stdio_transport
            session = await self.exit_stack.enter_async_context(
                ClientSession(read_stream, write_stream)
            )

            # Initialize the session
            await session.initialize()

            # Get available tools
            tools_result = await session.list_tools()
            available_tools = [
                {
                    'name': tool.name,
                    'description': tool.description or '',
                    'inputSchema': tool.inputSchema
                }
                for tool in tools_result.tools
            ]

            # Create a wrapper client that mimics the HTTP/SSE client interface
            stdio_wrapper = StdioMCPWrapper(session, available_tools)

            # Store connection info
            self.connections[server_id] = {
                'client': stdio_wrapper,
                'url': f"stdio://{command}",
                'protocol': 'stdio',
                'description': description,
                'connected_at': datetime.now(),
                'tools': available_tools,
                'session': session,  # Keep reference for cleanup
                'process_params': server_params
            }

            tool_names = [tool['name'] for tool in available_tools]
            logger.info(f"Successfully spawned {server_id} with tools: {', '.join(tool_names)}")
            return True

        except Exception as e:
            logger.error(f"Error spawning {server_id}: {e}")
            return False

    async def call_tool(self,
                       server_id: str, 
                       tool_name: str, 
                       tool_input: Any,
                       tool_call_id: str = "manual_call") -> Dict[str, Any]:
        """Call a tool on a specific server.
        
        Args:
            server_id: ID of the server to call the tool on
            tool_name: Name of the tool to call
            tool_input: Input for the tool
            tool_call_id: ID for tracking the tool call
            
        Returns:
            Dictionary with result information
        """
        if server_id not in self.connections:
            return {
                'success': False,
                'error': f"Server {server_id} not connected",
                'server_id': server_id,
                'tool_name': tool_name
            }
            
        client = self.connections[server_id]['client']
        
        try:
            # Debug the tool call
            logger.info(f"About to call tool {tool_name} with input: {tool_input}")
            logger.info(f"Tool input type: {type(tool_input)}")
            logger.info(f"Client type: {type(client)}")
            logger.info(f"Client session status: {hasattr(client, 'session') and client.session is not None}")

            # Check if the client is properly connected
            if hasattr(client, 'available_tools'):
                logger.info(f"Available tools on client: {[tool['name'] for tool in client.available_tools]}")
                if tool_name not in [tool['name'] for tool in client.available_tools]:
                    logger.error(f"Tool {tool_name} not found in available tools!")

            # Try calling the tool with detailed debugging
            logger.info(f"Calling tool with call_id: {tool_call_id}")

            # Check if the client has a session and it's connected
            if hasattr(client, 'session') and client.session:
                logger.info(f"Session object exists: {type(client.session)}")
                logger.info(f"Session connected: {getattr(client.session, 'connected', 'unknown')}")

            # Try the tool call with explicit timeout (increased for web scraping)
            result = await client.call_tool(tool_name, tool_input, tool_call_id, timeout=120.0)

            logger.info(f"Tool call result: success={result.success}, error={result.error}")
            logger.info(f"Result content type: {type(result.content)}")

            return {
                'success': result.success,
                'content': result.content,
                'error': result.error,
                'server_id': server_id,
                'tool_name': tool_name,
                'execution_time': result.execution_time
            }
            
        except Exception as e:
            logger.error(f"Error calling tool {tool_name} on {server_id}: {e}")
            logger.error(f"Tool input was: {tool_input}")
            logger.error(f"Exception details: {type(e).__name__}: {e}")
            logger.error(f"Exception repr: {repr(e)}")
            logger.error(f"Exception args: {e.args}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e) if str(e) else f"Unknown {type(e).__name__} exception",
                'server_id': server_id,
                'tool_name': tool_name
            }
    
    async def process_query(self, 
                           query: str, 
                           server_id: Optional[str] = None,
                           **kwargs) -> Dict[str, Any]:
        """Process a query using a specific server or the first available server.
        
        Args:
            query: The query to process
            server_id: Optional server ID. If not provided, uses first available server
            **kwargs: Additional arguments for query processing
            
        Returns:
            Query result
        """
        target_server = server_id
        if not target_server:
            if not self.connections:
                return {'error': 'No servers connected'}
            target_server = next(iter(self.connections.keys()))
            
        if target_server not in self.connections:
            return {'error': f'Server {target_server} not connected'}
            
        client = self.connections[target_server]['client']
        
        try:
            result = await client.process_query(query, **kwargs)
            result['server_id'] = target_server
            return result
        except Exception as e:
            logger.error(f"Error processing query on {target_server}: {e}")
            return {'error': str(e), 'server_id': target_server}

    async def remove_server(self, server_id: str) -> bool:
        """Remove a server connection."""
        try:
            if server_id in self.connections:
                connection_info = self.connections[server_id]
                client = connection_info.get('client')

                # Cleanup the client connection with timeout
                if client and hasattr(client, 'cleanup'):
                    try:
                        await asyncio.wait_for(client.cleanup(), timeout=3.0)
                        logger.info(f"Gracefully cleaned up client for {server_id}")
                    except asyncio.TimeoutError:
                        logger.warning(f"Cleanup timeout for {server_id} (proceeding anyway)")
                    except Exception as cleanup_error:
                        logger.warning(f"Cleanup error for {server_id}: {cleanup_error} (proceeding anyway)")

                # Remove from connections
                del self.connections[server_id]
                logger.info(f"Removed server: {server_id}")
                return True
            else:
                logger.warning(f"Server {server_id} not found in connections")
                return False

        except Exception as e:
            logger.error(f"Error removing server {server_id}: {e}")
            # Still remove from connections even if cleanup failed
            if server_id in self.connections:
                try:
                    del self.connections[server_id]
                    logger.info(f"Force-removed {server_id} after cleanup error")
                except Exception:
                    pass  # Ignore secondary errors
            return False

    def list_servers(self) -> Dict[str, Dict[str, Any]]:
        """List all connected servers and their tools."""
        return {
            server_id: {
                'url': info['url'],
                'protocol': info['protocol'],
                'description': info['description'],
                'connected_at': info['connected_at'].isoformat(),
                'tools': [tool['name'] for tool in info['tools']]
            }
            for server_id, info in self.connections.items()
        }
    
    def get_all_tools(self) -> Dict[str, List[str]]:
        """Get all available tools grouped by server."""
        return {
            server_id: [tool['name'] for tool in info['tools']]
            for server_id, info in self.connections.items()
        }
    
    async def find_tool_server(self, tool_name: str) -> Optional[str]:
        """Find which server has a specific tool.

        Args:
            tool_name: Name of the tool to find

        Returns:
            Server ID that has the tool, or None if not found
        """
        for server_id, info in self.connections.items():
            tool_names = [tool['name'] for tool in info['tools']]
            if tool_name in tool_names:
                return server_id
        return None

    async def call_tool_auto(self,
                            tool_name: str,
                            tool_input: Any,
                            tool_call_id: str = "auto_call") -> Dict[str, Any]:
        """Automatically find and call a tool on the appropriate server.

        Args:
            tool_name: Name of the tool to call
            tool_input: Input for the tool
            tool_call_id: ID for tracking the tool call

        Returns:
            Dictionary with result information
        """
        server_id = await self.find_tool_server(tool_name)
        if not server_id:
            return {
                'success': False,
                'error': f"Tool {tool_name} not found on any connected server",
                'tool_name': tool_name
            }

        return await self.call_tool(server_id, tool_name, tool_input, tool_call_id)

    async def get_aggregated_tools(self) -> List[Dict[str, Any]]:
        """Get all tools from all servers in Anthropic format.

        Returns:
            List of tools in Anthropic API format with server info
        """
        aggregated_tools = []

        for server_id, info in self.connections.items():
            for tool in info['tools']:
                # Add server info to tool definition
                tool_with_server = tool.copy()
                tool_with_server['_server_id'] = server_id
                tool_with_server['_server_url'] = info['url']
                aggregated_tools.append(tool_with_server)

        return aggregated_tools

    async def cleanup(self):
        """Clean up all connections with improved error handling."""
        logger.info("MCP: Cleaning up resources...")
        cleanup_errors = []

        # Create a copy of connections to avoid modification during iteration
        connections_to_cleanup = dict(self.connections)

        # Clear connections first to prevent new operations
        self.connections.clear()

        # Cleanup each connection with individual error handling
        for server_id, info in connections_to_cleanup.items():
            try:
                client = info['client']
                if hasattr(client, 'cleanup'):
                    # Use shorter timeout and better error isolation
                    await asyncio.wait_for(client.cleanup(), timeout=2.0)
                    logger.info(f"Cleaned up connection to {server_id}")
            except asyncio.TimeoutError:
                logger.warning(f"Cleanup timeout for {server_id} (non-critical)")
                cleanup_errors.append(f"{server_id}: timeout")
            except Exception as e:
                logger.warning(f"Error cleaning up {server_id} (non-critical): {e}")
                cleanup_errors.append(f"{server_id}: {e}")

        # Cleanup the exit stack with better error handling
        if hasattr(self, 'exit_stack') and self.exit_stack:
            try:
                await asyncio.wait_for(self.exit_stack.aclose(), timeout=1.0)
                logger.info("Exit stack cleaned up successfully")
            except asyncio.TimeoutError:
                logger.warning("Exit stack cleanup timeout (non-critical)")
            except Exception as e:
                logger.warning(f"Exit stack cleanup error (non-critical): {e}")

        if cleanup_errors:
            logger.info(f"Cleanup completed with {len(cleanup_errors)} non-critical warnings")
        else:
            logger.info("MCP: Cleanup complete.")


async def main():
    """Example usage of the MultiMCPClient."""
    
    # Create the multi-MCP client
    client = MultiMCPClient()
    
    try:
        # Add multiple servers
        print("Connecting to multiple MCP servers...")
        
        # Add local HTTP server
        await client.add_server(
            server_id="gaia_http",
            server_url="http://0.0.0.0:9000/mcp",
            protocol="http",
            description="Local Gaia MCP server (HTTP)"
        )
        
        # Add local SSE server (if available)
        await client.add_server(
            server_id="gaia_sse", 
            server_url="http://0.0.0.0:9000/sse",
            protocol="sse",
            description="Local Gaia MCP server (SSE)"
        )
        
        # List connected servers
        print("\nConnected servers:")
        servers = client.list_servers()
        for server_id, info in servers.items():
            print(f"  {server_id}: {info['description']}")
            print(f"    URL: {info['url']} ({info['protocol']})")
            print(f"    Tools: {', '.join(info['tools'])}")
            print()
        
        # Get all available tools
        print("All available tools:")
        all_tools = client.get_all_tools()
        for server_id, tools in all_tools.items():
            print(f"  {server_id}: {', '.join(tools)}")
        print()
        
        # Example: Call a tool directly
        if "gaia_http" in client.connections:
            print("Calling echostring tool on gaia_http server...")
            result = await client.call_tool(
                server_id="gaia_http",
                tool_name="echostring", 
                tool_input="Hello from multi-MCP client!"
            )
            
            if result['success']:
                print(f"Tool result: {result['content']}")
            else:
                print(f"Tool error: {result['error']}")
            print()
        
        # Example: Auto-find and call a tool
        print("Auto-calling echostring tool (will find the right server)...")
        auto_result = await client.call_tool_auto(
            tool_name="echostring",
            tool_input="Auto-routed tool call!"
        )

        if auto_result['success']:
            print(f"Auto tool result: {auto_result['content']}")
            print(f"Tool was called on server: {auto_result['server_id']}")
        else:
            print(f"Auto tool error: {auto_result['error']}")
        print()

        # Example: Get aggregated tools from all servers
        print("Getting aggregated tools from all servers...")
        all_tools = await client.get_aggregated_tools()
        print(f"Total tools available: {len(all_tools)}")
        for tool in all_tools[:3]:  # Show first 3 tools
            print(f"  {tool['name']} (from {tool['_server_id']}): {tool['description']}")
        print()

        # Example: Process a query using LLM + tools
        print("Processing a query with LLM...")
        query_result = await client.process_query(
            "Echo the phrase 'Multi-server MCP is working!' using available tools",
            server_id="gaia_http"  # Specify which server to use
        )

        if 'error' not in query_result:
            print(f"Query result: {query_result.get('final_text', 'No response')}")
        else:
            print(f"Query error: {query_result['error']}")

    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up connections
        print("\nCleaning up connections...")
        await client.cleanup()
        print("Done!")


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
