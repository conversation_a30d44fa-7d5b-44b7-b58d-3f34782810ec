#!/usr/bin/env python3
"""
Nested MCP Server - Meta-Server Approach

This script creates a "meta-MCP server" that connects to multiple underlying MCP servers
and exposes all their tools through a single unified MCP interface.

Architecture:
    Client -> Meta-MCP Server -> Multiple Backend MCP Servers
    
Benefits:
    - Single connection point for clients
    - Transparent tool aggregation
    - Load balancing and failover
    - Tool namespacing to avoid conflicts
    - Centralized logging and monitoring

Usage:
    python nested_mcp_server.py --port 8080 --config nested_servers.json
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, List, Optional, Any, Union
from contextlib import AsyncExitStack

try:
    from mcp.server.fastmcp import FastMCP, Context
    from mcp import types
    from .multi_mcp_client import MultiMCPClient
except ImportError as e:
    print(f"Error importing required libraries: {e}")
    print("Make sure you're running this from the correct directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NestedMCPServer:
    """A meta-MCP server that aggregates multiple backend MCP servers."""
    
    def __init__(self, config_file: str = "nested_servers.json"):
        """Initialize the nested MCP server.
        
        Args:
            config_file: Path to configuration file defining backend servers
        """
        self.config_file = config_file
        self.backend_client = MultiMCPClient()
        self.server_map: Dict[str, str] = {}  # tool_name -> server_id
        self.tool_prefix_map: Dict[str, str] = {}  # prefixed_tool -> original_tool
        self.mcp_server = FastMCP("nested_mcp_server")
        
    async def load_backend_servers(self) -> bool:
        """Load and connect to backend servers from config file."""
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_file}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return False
        
        backend_servers = config.get('backend_servers', {})
        if not backend_servers:
            logger.error("No backend servers defined in configuration")
            return False
        
        success_count = 0
        
        for server_id, server_config in backend_servers.items():
            if not server_config.get('enabled', True):
                logger.info(f"Skipping disabled backend server: {server_id}")
                continue
            
            url = server_config.get('url')
            protocol = server_config.get('protocol', 'http')
            description = server_config.get('description', '')
            
            if not url:
                logger.error(f"No URL specified for backend server: {server_id}")
                continue
            
            # Handle environment variable substitution
            if '{' in url and '}' in url:
                try:
                    url = url.format(**os.environ)
                except KeyError as e:
                    logger.error(f"Environment variable not found for {server_id}: {e}")
                    continue
            
            # Connect to backend server
            success = await self.backend_client.add_server(
                server_id=server_id,
                server_url=url,
                protocol=protocol,
                description=description
            )
            
            if success:
                success_count += 1
                await self._register_backend_tools(server_id, server_config)
        
        logger.info(f"Connected to {success_count}/{len(backend_servers)} backend servers")
        return success_count > 0
    
    async def _register_backend_tools(self, server_id: str, server_config: Dict[str, Any]):
        """Register tools from a backend server with the meta-server."""
        connection = self.backend_client.connections.get(server_id)
        if not connection:
            return
        
        use_prefix = server_config.get('use_prefix', False)
        prefix = server_config.get('prefix', server_id)
        
        for tool in connection['tools']:
            original_tool_name = tool['name']
            
            # Create tool name (with or without prefix)
            if use_prefix:
                meta_tool_name = f"{prefix}_{original_tool_name}"
                self.tool_prefix_map[meta_tool_name] = original_tool_name
            else:
                meta_tool_name = original_tool_name
            
            # Check for tool name conflicts
            if meta_tool_name in self.server_map:
                logger.warning(f"Tool name conflict: {meta_tool_name} exists on multiple servers")
                # Use prefixed version to resolve conflict
                meta_tool_name = f"{server_id}_{original_tool_name}"
                self.tool_prefix_map[meta_tool_name] = original_tool_name
            
            # Map tool to server
            self.server_map[meta_tool_name] = server_id
            
            # Create the meta-tool function
            await self._create_meta_tool(meta_tool_name, original_tool_name, server_id, tool)
    
    async def _create_meta_tool(self, meta_tool_name: str, original_tool_name: str, 
                               server_id: str, tool_schema: Dict[str, Any]):
        """Create a meta-tool that proxies to a backend server tool."""
        
        async def meta_tool_func(ctx: Context, **kwargs) -> str:
            """Meta-tool function that proxies to backend server."""
            try:
                # Report progress
                await ctx.report_progress(progress=0, total=2, message=f"Calling {original_tool_name} on {server_id}")
                
                # Call the backend tool
                result = await self.backend_client.call_tool(
                    server_id=server_id,
                    tool_name=original_tool_name,
                    tool_input=kwargs,
                    tool_call_id=f"meta_{meta_tool_name}"
                )
                
                await ctx.report_progress(progress=1, total=2, message="Processing result")
                
                if result['success']:
                    await ctx.report_progress(progress=2, total=2, message="Complete")
                    return result['content']
                else:
                    error_msg = f"Backend tool error: {result['error']}"
                    logger.error(error_msg)
                    return error_msg
                    
            except Exception as e:
                error_msg = f"Meta-tool error calling {original_tool_name} on {server_id}: {e}"
                logger.error(error_msg)
                return error_msg
        
        # Update tool schema for meta-server
        meta_tool_schema = tool_schema.copy()
        meta_tool_schema['description'] = f"[{server_id}] {tool_schema['description']}"
        
        # Register the meta-tool with FastMCP
        self.mcp_server.add_tool(
            func=meta_tool_func,
            name=meta_tool_name,
            description=meta_tool_schema['description'],
            input_schema=meta_tool_schema['inputSchema']
        )
        
        logger.info(f"Registered meta-tool: {meta_tool_name} -> {server_id}.{original_tool_name}")
    
    async def add_meta_tools(self):
        """Add meta-server specific tools."""
        
        @self.mcp_server.tool()
        async def list_backend_servers(ctx: Context) -> str:
            """List all connected backend servers and their status."""
            servers = self.backend_client.list_servers()
            
            result = "Connected Backend Servers:\n"
            for server_id, info in servers.items():
                result += f"\n{server_id}:\n"
                result += f"  URL: {info['url']}\n"
                result += f"  Protocol: {info['protocol']}\n"
                result += f"  Description: {info['description']}\n"
                result += f"  Tools: {', '.join(info['tools'])}\n"
            
            return result
        
        @self.mcp_server.tool()
        async def get_tool_mapping(ctx: Context) -> str:
            """Get the mapping of meta-tools to backend servers."""
            result = "Tool Mapping (Meta-tool -> Backend Server):\n"
            
            for meta_tool, server_id in self.server_map.items():
                original_tool = self.tool_prefix_map.get(meta_tool, meta_tool)
                result += f"  {meta_tool} -> {server_id}.{original_tool}\n"
            
            return result
        
        @self.mcp_server.tool()
        async def backend_server_health(ctx: Context) -> str:
            """Check health status of all backend servers."""
            servers = self.backend_client.list_servers()
            
            result = "Backend Server Health:\n"
            for server_id, info in servers.items():
                # Try a simple tool call to test connectivity
                try:
                    # Look for a simple tool to test with
                    test_tools = ['echostring', 'health_check', 'ping']
                    test_tool = None
                    
                    for tool_name in test_tools:
                        if tool_name in info['tools']:
                            test_tool = tool_name
                            break
                    
                    if test_tool:
                        test_result = await self.backend_client.call_tool(
                            server_id=server_id,
                            tool_name=test_tool,
                            tool_input="health_check",
                            tool_call_id="health_test"
                        )
                        status = "✓ Healthy" if test_result['success'] else "✗ Error"
                    else:
                        status = "? No test tool available"
                        
                except Exception as e:
                    status = f"✗ Connection error: {e}"
                
                result += f"  {server_id}: {status}\n"
            
            return result
    
    async def start_server(self, port: int = 8080, host: str = "0.0.0.0"):
        """Start the nested MCP server."""
        logger.info(f"Starting nested MCP server on {host}:{port}")
        
        # Load backend servers
        success = await self.load_backend_servers()
        if not success:
            logger.error("Failed to connect to backend servers")
            return False
        
        # Add meta-tools
        await self.add_meta_tools()
        
        # Start the FastMCP server
        try:
            await self.mcp_server.run(
                transport="http",
                http_port=port,
                http_host=host
            )
        except Exception as e:
            logger.error(f"Error starting server: {e}")
            return False
        
        return True
    
    async def cleanup(self):
        """Clean up backend connections."""
        await self.backend_client.cleanup()


def create_example_nested_config():
    """Create an example configuration file for nested servers."""
    config = {
        "description": "Nested MCP Server Configuration",
        "meta_server": {
            "name": "Unified MCP Gateway",
            "description": "Meta-server aggregating multiple MCP backends",
            "port": 8080,
            "host": "0.0.0.0"
        },
        "backend_servers": {
            "gaia_http": {
                "enabled": True,
                "url": "http://0.0.0.0:9000/mcp",
                "protocol": "http",
                "description": "Local Gaia MCP server (HTTP)",
                "use_prefix": False,
                "prefix": "gaia"
            },
            "gaia_sse": {
                "enabled": True,
                "url": "http://0.0.0.0:9000/sse",
                "protocol": "sse", 
                "description": "Local Gaia MCP server (SSE)",
                "use_prefix": True,
                "prefix": "sse"
            },
            "firecrawl": {
                "enabled": False,
                "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
                "protocol": "sse",
                "description": "Firecrawl hosted MCP server",
                "use_prefix": True,
                "prefix": "web",
                "environment_required": ["FIRECRAWL_API_KEY"]
            }
        }
    }
    
    config_file = "nested_servers.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Created example nested server configuration: {config_file}")
    return config_file


async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Nested MCP Server")
    parser.add_argument("--port", type=int, default=8080, help="Port to run on")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--config", default="nested_servers.json", help="Configuration file")
    parser.add_argument("--create-config", action="store_true", help="Create example config and exit")
    
    args = parser.parse_args()
    
    if args.create_config:
        create_example_nested_config()
        return
    
    # Create example config if it doesn't exist
    if not os.path.exists(args.config):
        print(f"Configuration file not found: {args.config}")
        print("Creating example configuration...")
        create_example_nested_config()
        print(f"Edit {args.config} to configure your backend servers, then run this script again.")
        return
    
    # Create and start the nested server
    nested_server = NestedMCPServer(args.config)
    
    try:
        await nested_server.start_server(args.port, args.host)
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await nested_server.cleanup()
        print("Cleanup complete.")


if __name__ == "__main__":
    asyncio.run(main())
