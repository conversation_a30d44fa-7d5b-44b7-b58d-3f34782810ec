# Enhanced MCP HTTP Server Guide

This guide shows how to use the enhanced MCP HTTP server that provides both local tools AND delegates to third-party MCP servers, all through the same port 9000 that `chat_term` already connects to.

## Architecture

```
chat_term -> Enhanced MCP Server (Port 9000) -> Local Tools + Third-party MCP Servers
```

**Benefits:**
- No changes to `chat_term` connection
- Same port 9000 as before
- Local tools + third-party tools in one place
- Namespaced third-party tools (e.g., `web.firecrawl_scrape`)
- Configuration-driven third-party setup

## Quick Setup

### 1. Create Configuration (Optional)

```bash
# Create example configuration for third-party servers
python mcp_http_server_enhanced.py --create-config
```

This creates `server_config.json`:

```json
{
  "description": "Enhanced MCP Server Configuration",
  "third_party_servers": {
    "firecrawl": {
      "enabled": false,
      "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
      "protocol": "sse",
      "description": "Firecrawl hosted MCP server",
      "namespace": "web",
      "environment_required": ["FIRECRAWL_API_KEY"]
    },
    "demo_server": {
      "enabled": false,
      "url": "http://localhost:8001/mcp",
      "protocol": "http",
      "description": "Demo MCP server",
      "namespace": "demo"
    }
  }
}
```

### 2. Start Enhanced Server

```bash
# Start with default configuration (local tools only)
python mcp_http_server_enhanced.py --port 9000

# Start with third-party server configuration
python mcp_http_server_enhanced.py --port 9000 --config server_config.json
```

### 3. Connect chat_term (Same as Before!)

```bash
# Same command as always - no changes needed!
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp
```

## Available Tools

### Local Tools (Always Available)
- `echostring` - Echo a string back
- `echostring_table` - Create a table from items
- `long_task` - Long-running task with progress
- `firecrawl_scrape_text_only` - Local web scraping
- `server_status` - Get server and third-party connection status
- `list_all_tools` - List all available tools (local + third-party)
- `third_party_health` - Check health of third-party connections

### Third-party Tools (When Configured)
- `web.firecrawl_scrape` - Firecrawl web scraping (if Firecrawl server enabled)
- `web.firecrawl_search` - Firecrawl web search (if Firecrawl server enabled)
- `demo.some_tool` - Tools from demo server (if demo server enabled)

## Configuration Options

### Enable Firecrawl Integration

1. Set environment variable:
```bash
export FIRECRAWL_API_KEY="your_api_key_here"
```

2. Edit `server_config.json`:
```json
{
  "third_party_servers": {
    "firecrawl": {
      "enabled": true,
      "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
      "protocol": "sse",
      "namespace": "web"
    }
  }
}
```

3. Restart the server:
```bash
python mcp_http_server_enhanced.py --port 9000 --config server_config.json
```

### Add Custom Third-party Server

```json
{
  "third_party_servers": {
    "my_server": {
      "enabled": true,
      "url": "http://my-server:8080/mcp",
      "protocol": "http",
      "description": "My custom MCP server",
      "namespace": "custom"
    }
  }
}
```

## Usage Examples in chat_term

### Local Tools
```
> Use echostring to echo "Hello from local server"
> Create a table with echostring_table using items ["A", "B", "C"]
> Run long_task for 10 seconds to test progress reporting
> Get server_status to see all connections
```

### Third-party Tools (if configured)
```
> Use web.firecrawl_scrape to scrape https://example.com
> Use web.firecrawl_search to search for "AI news"
> Check third_party_health to verify all connections
> List all available tools with list_all_tools
```

### Server Management
```
> Get server_status to see local and third-party server info
> Check third_party_health to verify third-party connections
> Use list_all_tools to see everything available
```

## Comparison with Original Server

### Original `mcp_http_server.py`
- Only local tools
- Simple, lightweight
- No third-party integration

### Enhanced `mcp_http_server_enhanced.py`
- Local tools + third-party delegation
- Namespaced third-party tools
- Configuration-driven
- Server management tools
- Same port and interface

## Migration from Original Server

### No Changes Needed for chat_term
The enhanced server is a drop-in replacement:

```bash
# Before (original server)
python mcp_http_server.py --port 9000

# After (enhanced server)
python mcp_http_server_enhanced.py --port 9000
```

`chat_term` connects the same way and gets all the original tools plus any configured third-party tools.

### Gradual Third-party Addition
1. Start with enhanced server (no config) - same as original
2. Add configuration file when ready for third-party tools
3. Enable third-party servers one by one
4. No disruption to existing workflows

## Troubleshooting

### Enhanced Server Won't Start
1. Check if port 9000 is available
2. Verify configuration file syntax
3. Check environment variables for third-party servers
4. Review server logs for specific errors

### Third-party Tools Not Available
1. Use `server_status` to check third-party connections
2. Use `third_party_health` to test connectivity
3. Verify third-party server URLs and credentials
4. Check third-party server logs

### Performance Issues
1. Use `third_party_health` to identify slow connections
2. Check network connectivity to third-party servers
3. Consider disabling problematic third-party servers
4. Monitor server logs for timeout errors

## Advanced Configuration

### Custom Namespaces
```json
{
  "third_party_servers": {
    "firecrawl": {
      "namespace": "scrape"  // Tools become scrape.firecrawl_scrape
    }
  }
}
```

### Environment Variable Substitution
```json
{
  "third_party_servers": {
    "api_server": {
      "url": "https://api.example.com/{API_TOKEN}/mcp",
      "environment_required": ["API_TOKEN"]
    }
  }
}
```

### Conditional Enabling
```json
{
  "third_party_servers": {
    "optional_server": {
      "enabled": false,  // Disable this server
      "url": "http://optional:9000/mcp"
    }
  }
}
```

## Development Workflow

### Local Development
```bash
# Start enhanced server with local tools only
python mcp_http_server_enhanced.py --port 9000

# Connect chat_term
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp
```

### Testing Third-party Integration
```bash
# Create config with test servers
python mcp_http_server_enhanced.py --create-config

# Edit server_config.json to enable test servers
# Start with third-party integration
python mcp_http_server_enhanced.py --port 9000 --config server_config.json
```

### Production Deployment
```bash
# Set environment variables
export FIRECRAWL_API_KEY="prod_key"
export OTHER_API_KEY="prod_key"

# Start with production config
python mcp_http_server_enhanced.py --port 9000 --config production_config.json
```

This enhanced server approach gives you the best of both worlds: all your existing functionality continues to work exactly the same, but you can gradually add third-party MCP server integration as needed, all through the same familiar interface that `chat_term` already uses!
