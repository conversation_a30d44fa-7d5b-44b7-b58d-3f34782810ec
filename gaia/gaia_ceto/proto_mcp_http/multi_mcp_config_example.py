#!/usr/bin/env python3
"""
Multi-MCP Client with Configuration Example

This script shows how to use a configuration file to define multiple MCP servers
and connect to them automatically.

Usage:
    python multi_mcp_config_example.py [config_file]

Default config file: mcp_servers_config.json
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, List, Optional, Any

try:
    from .multi_mcp_client import MultiMCPClient
except ImportError:
    print("Error: multi_mcp_client.py not found. Make sure it's in the same directory.")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConfigurableMultiMCPClient(MultiMCPClient):
    """Extended MultiMCPClient that supports configuration files."""
    
    async def load_from_config(self, config_file: str) -> bool:
        """Load server configurations from a JSON file.
        
        Args:
            config_file: Path to the configuration file
            
        Returns:
            True if all servers connected successfully
        """
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {config_file}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return False
        
        servers = config.get('servers', {})
        if not servers:
            logger.error("No servers defined in configuration")
            return False
        
        success_count = 0
        total_count = len(servers)
        
        for server_id, server_config in servers.items():
            # Skip disabled servers
            if not server_config.get('enabled', True):
                logger.info(f"Skipping disabled server: {server_id}")
                continue
            
            # Extract server configuration
            url = server_config.get('url')
            protocol = server_config.get('protocol', 'http')
            description = server_config.get('description', '')
            
            if not url:
                logger.error(f"No URL specified for server: {server_id}")
                continue
            
            # Handle environment variable substitution in URL
            if '{' in url and '}' in url:
                try:
                    url = url.format(**os.environ)
                except KeyError as e:
                    logger.error(f"Environment variable not found for {server_id}: {e}")
                    continue
            
            # Connect to the server
            success = await self.add_server(
                server_id=server_id,
                server_url=url,
                protocol=protocol,
                description=description
            )
            
            if success:
                success_count += 1
        
        logger.info(f"Connected to {success_count}/{total_count} servers")
        return success_count > 0


def create_example_config():
    """Create an example configuration file."""
    config = {
        "description": "Example MCP servers configuration",
        "servers": {
            "gaia_local_http": {
                "enabled": True,
                "url": "http://0.0.0.0:9000/mcp",
                "protocol": "http",
                "description": "Local Gaia MCP server (HTTP)"
            },
            "gaia_local_sse": {
                "enabled": True,
                "url": "http://0.0.0.0:9000/sse", 
                "protocol": "sse",
                "description": "Local Gaia MCP server (SSE)"
            },
            "firecrawl_hosted": {
                "enabled": False,
                "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
                "protocol": "sse",
                "description": "Firecrawl hosted MCP server (requires API key)",
                "environment_required": ["FIRECRAWL_API_KEY"]
            },
            "demo_server": {
                "enabled": False,
                "url": "http://0.0.0.0:8001/mcp",
                "protocol": "http", 
                "description": "Demo MCP server"
            }
        }
    }
    
    config_file = "mcp_servers_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Created example configuration file: {config_file}")
    return config_file


async def interactive_demo(client: ConfigurableMultiMCPClient):
    """Run an interactive demo with the connected servers."""
    
    print("\n" + "="*50)
    print("INTERACTIVE MULTI-MCP CLIENT DEMO")
    print("="*50)
    
    # Show connected servers
    servers = client.list_servers()
    print(f"\nConnected to {len(servers)} servers:")
    for server_id, info in servers.items():
        print(f"  {server_id}: {info['description']}")
        print(f"    Tools: {', '.join(info['tools'])}")
    
    if not servers:
        print("No servers connected. Exiting.")
        return
    
    print("\nAvailable commands:")
    print("  list - List all servers and tools")
    print("  call <tool_name> <input> - Call a tool (auto-routed)")
    print("  query <question> - Ask a question using LLM + tools")
    print("  quit - Exit the demo")
    
    while True:
        try:
            command = input("\n> ").strip()
            
            if not command:
                continue
            elif command == "quit":
                break
            elif command == "list":
                all_tools = client.get_all_tools()
                for server_id, tools in all_tools.items():
                    print(f"  {server_id}: {', '.join(tools)}")
            elif command.startswith("call "):
                parts = command.split(" ", 2)
                if len(parts) < 3:
                    print("Usage: call <tool_name> <input>")
                    continue
                
                tool_name = parts[1]
                tool_input = parts[2]
                
                print(f"Calling tool '{tool_name}' with input: {tool_input}")
                result = await client.call_tool_auto(tool_name, tool_input)
                
                if result['success']:
                    print(f"Result: {result['content']}")
                    print(f"(Called on server: {result.get('server_id', 'unknown')})")
                else:
                    print(f"Error: {result['error']}")
                    
            elif command.startswith("query "):
                question = command[6:]  # Remove "query "
                print(f"Processing query: {question}")
                
                # Use the first available server
                server_id = next(iter(servers.keys()))
                result = await client.process_query(question, server_id=server_id)
                
                if 'error' not in result:
                    print(f"Response: {result.get('final_text', 'No response')}")
                else:
                    print(f"Error: {result['error']}")
            else:
                print("Unknown command. Type 'quit' to exit.")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")


async def main():
    """Main function."""
    
    # Get config file from command line or use default
    config_file = sys.argv[1] if len(sys.argv) > 1 else "mcp_servers_config.json"
    
    # Create example config if it doesn't exist
    if not os.path.exists(config_file):
        print(f"Configuration file not found: {config_file}")
        print("Creating example configuration...")
        config_file = create_example_config()
        print(f"Edit {config_file} to configure your servers, then run this script again.")
        return
    
    # Create and configure the client
    client = ConfigurableMultiMCPClient()
    
    try:
        print(f"Loading configuration from: {config_file}")
        success = await client.load_from_config(config_file)
        
        if not success:
            print("Failed to connect to any servers. Check your configuration.")
            return
        
        # Run interactive demo
        await interactive_demo(client)
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up
        print("\nCleaning up connections...")
        await client.cleanup()
        print("Done!")


if __name__ == "__main__":
    asyncio.run(main())
